@view-transition {
    navigation: auto;
}

@layer reset {

*,
*::before,
*::after {
    box-sizing: border-box;
}

/* https://kilianvalkhof.com/2022/css-html/your-css-reset-needs-text-size-adjust-probably/ */
html {
    -moz-text-size-adjust: none;
    -webkit-text-size-adjust: none;
    text-size-adjust: none;
}

body,
h1,
h2,
h3,
h4,
p,
figure,
blockquote,
dl,
dd {
    margin: 0;
}

/* https://www.scottohara.me/blog/2019/01/12/lists-and-safari.html */
[role="list"] {
    list-style: none;
    margin: 0;
    padding: 0;
}

body {
    min-block-size: 100vh;
    line-height: 1.6;
}

h1,
h2,
h3,
button,
input,
label {
    line-height: 1.1;
}

h1,
h2,
h3,
h4 {
    text-wrap: balance;
}

p,
li {
    text-wrap: pretty;
}

img,
picture {
    max-inline-size: 100%;
    display: block;
}

input,
button,
textarea,
select {
    font: inherit;
}

}

@layer base {
    :root {
        /*colors*/
        --clr-white: hsl(0, 0%, 100%);
        --clr-gray-100: hsl(0, 2%, 79%);
        --clr-brand-400: hsl(25, 88%, 75%);
        --clr-brand-500: hsl(25, 88%, 66%);
        --clr-green-400: hsl(143, 19%, 49%);
        --clr-green-500: hsl(143, 38%, 37%);
        --clr-green-600: hsl(145, 29%, 19%);
        --clr-brown-500: hsl(10, 5%, 25%);
        --clr-brown-600: hsl(9, 7%, 21%);
        --clr-brown-700: hsl(9, 8%, 16%);
        --clr-brown-800: hsl(0, 6%, 15%);
        --clr-brown-900: hsl(0, 6%, 13%);

        --clr-orange-500: hsl(28, 43%, 28%);
        --clr-red-500: hsl(359, 34%, 24%);
        --clr-teal-500: hsl(186, 42%, 25%);

        /*typography*/
        --ff-heading: "Outfit", sans-serif;
        --ff-body: "Fira Sans", sans-serif;

        --fs-300: 0.875rem;
        --fs-400: 1rem;
        --fs-500: 1.125rem;
        --fs-600: 1.25rem;
        --fs-700: 1.5rem;
        --fs-800: 2rem;
        --fs-900: 3.75rem;
        --fs-1000: 3.75rem;

        @media (width > 760px) {
            --fs-300: 0.875rem;
            --fs-400: 1rem;
            --fs-500: 1.25rem;
            --fs-600: 1.5rem;
            --fs-700: 2rem;
            --fs-800: 3rem;
            --fs-900: 5rem;
            --fs-1000: 7.5rem;
        }
    }
    :root {
        /* primitives here */
        --text-main: var(--clr-gray-100);
        --text-high-contrast: var(--clr-white);
        --text-brand: var(--clr-brand-500);
        --text-brand-light: var(--clr-brand-400);

        --background-accent-light: var(--clr-green-400);
        --background-accent-main: var(--clr-green-500);
        --background-accent-dark: var(--clr-green-600);

        --background-extra-light: var(--clr-brown-500);
        --background-light: var(--clr-brown-600);
        --background-main: var(--clr-brown-700);
        --background-dark: var(--clr-brown-800);
        --background-extra-dark: var(--clr-brown-900);

        --font-size-heading-sm: var(--fs-700);
        --font-size-heading-regular: var(--fs-800);
        --font-size-heading-lg: var(--fs-900);
        --font-size-heading-xl: var(--fs-1000);

        --font-size-sm: var(--fs-300);
        --font-size-regular: var(--fs-400);
        --font-size-md: var(--fs-500);
        --font-size-lg: var(--fs-600);

        --border-radius-1: 0.25rem;
        --border-radius-2: 0.5rem;
        --border-radius-3: 0.75rem;
    }

    html {
        font-family: var(--ff-body), sans-serif;
        line-height: 1.6;
    }

    @media (prefers-reduced-motion: no-preference) {
        html {
            scroll-behavior: smooth;
            scroll-padding: 3rem;
        }
    }

    body {
        font-family: var(--ff-body), sans-serif;
        font-size: var(--font-size-regular);
        color: var(--text-main);
        background-color: var(--background-main);
    }
    h1,
    h2,
    h3,
    h4 {
        font-family: var(--ff-heading), sans-serif;
        font-weight: 700;
        color: var(--text-high-contrast);
    }

    a {
        color: var(--text-high-contrast);
    }

    a:hover,
    a:focus-visible {
        color: var(--text-brand-light);
    }

    img {
        border-radius: var(--border-radius-3);
    }
}

@layer layout {

    .flex-group{
        display: flex;
        flex-wrap: wrap;
        gap: .5rem;
    }

    .section {
        padding-block: 3.75rem;

        @media (min-width: 760px) {
            padding-block: 8rem;

            &[data-padding="compact"] {
                padding-block: 4.5rem;
            }
        }
    }

    .wrapper {
        --wrapper-max-width: 1130px;
        --wrapper-padding: 1rem;

        max-width: var(--wrapper-max-width);
        margin-inline: auto;
        padding-inline: var(--wrapper-padding);
        /* helps to match the Figma file */
        box-sizing: content-box;

        &[data-width="narrow"] {
            --wrapper-max-width: 720px;
        }

        &[data-width="wide"] {
            --wrapper-max-width: 1330px;
        }
    }

    .equal-columns {
        display: grid;
        gap: var(--equal-columns-gap, 1rem);
        align-items: var(--equal-columns-vertical-aligment, stretch);

        @media (width > 760px) {
            grid-auto-flow: column;
            grid-auto-columns: 1fr;
        }

        &[data-gap="large"] {
            --equal-columns-gap: 2rem;
        }

        &[data-align="center"] {
            --equal-columns-vertical-aligment: center;
        }
    }

    .flow > * + * {
        --flow-space: 1em;
        margin-block-start: var(--flow-space, 1em);
    }

    .grid-flow {
        --grid-flow-space: 1rem;
        display: grid;
        gap: var(--grid-flow-space, 1rem);
    }

    .grid-auto-fill {
        --grid-auto-fit-min-column-size: 250px;
        display: grid;
        gap: 1rem;
        grid-template-columns: repeat(auto-fill, minmax(min(var(--grid-auto-fit-min-column-size, 450px), 100%), 1fr));
    }
}

@layer components{

    .skip-to-main {
        background: var(--clr-green-500);
        font-size: var(--fs-600);
        padding: 1rem;
        position: absolute;
        top: 1rem;
        left: 1rem;
        border-radius: var(--border-radius-3);
        color: white;
    }

    .skip-to-main:not(:focus) {
        clip: rect(0 0 0 0);
        clip-path: inset(50%);
        height: 1px;
        overflow: hidden;
        position: absolute;
        white-space: nowrap;
        width: 1px;
    }

    .site-header {
        padding-block: 1rem;
    }

    .site-header__inner {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem 2rem;
        justify-content: space-between;
    }

    [aria-controls="primary-nav"] {
        z-index: 200;
        background: transparent;
        border: 0;
        cursor: pointer;

        img {
            border-radius: 0;
        }
    }
    [aria-controls="primary-nav"]{
        display: none;
    }

    .primary-navigation {
        ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
        }

        a {
            text-decoration: none;
        }

        @media(width < 760px) {

            display: none;
            position: absolute;
            z-index: 10;
            top: 0;
            right: 0;
            font-weight: 700;
            font-size: var(--font-size-lg);
            font-family: var(--ff-heading), sans-serif;
            background-color: var(--background-accent-main);
            padding: 2rem;
            border-radius: 0 0 0 var(--border-radius-3);

            ul {
                gap: 0;
                flex-direction: column;
            }

            li + li {
                margin-block-start: 1.5rem;
                padding-block-start: 1.5rem;
                border-top: 2px solid var(--background-accent-light);
            }
        }
    }
    @media (width < 760px) {
        [aria-expanded="true"] + .primary-navigation {
            display: block;
        }
        [aria-controls="primary-nav"]{
            display: block;
        }
    }
    .hero {
        text-align: center;
        background-image: url(assets/hero.webp);
        background-size: cover;
        font-size: var(--font-size-md);
        color: var(--text-high-contrast);
    }

    .hero__title {
        font-size: var(--font-size-heading-lg);

        > span {
            display: block;
            font-size: var(--font-size-heading-xl);
            color: var(--text-brand);
        }
    }

    .mushroom-guide {
        --card-title-color: var(--text-high-contrast);
        --card-title-font-size: var(--font-size-lg);
        --card-gap: 0.75rem;
    }

    .faq-bento {
        color: var(--text-high-contrast);
        display: grid;
        gap: 1rem;

        grid-template-areas:
        "card-one"
        "card-two"
        "card-three"
        "card-four";

        @media (width > 600px) {
        grid-template-areas:
            "card-one card-two"
            "card-three card-four"
    }

        @media (width > 900px) {
            grid-template-areas:
                "card-one card-two card-three"
                "card-four card-four card-three";
        }

        .card > img {
            height: 100%;
            object-fit: cover;
        }

        @media (width > 600px) {
            .card:nth-child(even) > img {
                order: 3;
            }
        }


        :nth-child(1){
            grid-area: card-one;
        }
        :nth-child(2){
            grid-area: card-two;
        }
        :nth-child(3){
            grid-area: card-three;
        }
        .card:nth-child(4){
            grid-area: card-four;

            @media (width > 900px) {
                display: grid;
                grid-template-columns: 1fr 1fr;
                align-content: start;
                column-gap: 1rem;

                img {
                    grid-column: 1 / 2;
                    grid-row: 1 / 3;
                }

                h3 {
                    grid-column: 2 / 3;
                    grid-row: 1 / 2;
                }

                p {
                    grid-column: 2 / 3;
                    grid-row: 2 / 3;
                }
            }
        }
    }


    .card {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
        background-color: var(--background-light);
        border-radius: var(--border-radius-3);
        opacity: 1;
        transform: scale(1);
        transition: opacity 0.3s ease, transform 0.3s ease;

        img {
            border-radius: var(--border-radius-2);
        }
    }

    .card.card--hidden {
        display: none;
    }

    /* Enhanced view transitions for card filtering - ONLY for mushroom cards */
    ::view-transition-old(root) {
        animation: none;
    }

    ::view-transition-new(root) {
        animation: none;
    }

    /* Smooth transitions for individual mushroom cards only */
    [style*="mushroom-card"]::view-transition-old(*) {
        animation: fade-scale-out 0.3s ease-in forwards;
    }

    [style*="mushroom-card"]::view-transition-new(*) {
        animation: fade-scale-in 0.3s ease-out 0.1s forwards;
    }

    @keyframes fade-scale-out {
        from {
            opacity: 1;
            transform: scale(1);
        }
        to {
            opacity: 0;
            transform: scale(0.9);
        }
    }

    @keyframes fade-scale-in {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }
    .card__title {
        --card-title-color: var(--text-brand);
        --card-title-font-size: var(--font-size-heading-sm);
        color: var(--card-title-color, var(--text-brand));
        font-size: var(--card-title-font-size, var(--font-size-heading-sm));
    }

    .card__note {
        margin-block-start: auto;
        font-size: var(--font-size-sm);
        background-color: var(--background-extra-light);
        padding: 0.75rem;
        border-radius: var(--border-radius-1);
    }

    .tag-list{
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;

        li{
            font-family: var(--ff-heading), sans-serif;
            font-size: var(--font-size-sm);
            color: var(--text-high-contrast);
            --tag-bg: red;
            background-color: var(--tag-bg, red);
            padding: 0 0.25rem;
            border-radius: var(--border-radius-1);
        }
        [data-edible="edible"] {
            --tag-bg: var(--background-accent-main);
        }

        [data-edible="toxic"] {
            --tag-bg: var(--clr-red-500);
        }

        [data-season="spring"] {
            --tag-bg: var(--clr-teal-500);
        }

        [data-season="summer"] {
            --tag-bg: var(--background-accent-main);
        }
        [data-season="fall"] {
            --tag-bg: var(--clr-orange-500);
        }
    }

    .button {
        display: inline flex;
        cursor: pointer;
        font-family: var(--ff-heading), sans-serif;
        font-weight: 700;
        font-size: var(--font-size-md);
        text-decoration: none;
        background-color: var(--background-accent-main);
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius-1);
    }

    .button:hover,
    .button:focus-visible {
        background: var(--background-accent-light);
        color: var(--text-high-contrast);
    }

    .site-footer {
        background-color: var(--background-dark);
        padding-block: 2rem;
        text-align: center;

        /* either use .grid-flow and change the --grid-flow-spacing, or do this */
        display: grid;
        gap: 2rem;

        a {
            text-decoration: none;
            color: var(--text-main);

            &:hover,
            &:focus-visible {
                color: var(--text-brand-light);
            }
        }
    }

    .site-footer__title {
        font-size: var(--font-size-md);
        font-family: var(--ff-heading), sans-serif;
        font-weight: 700;
    }

    select{
        padding: .5rem 1rem;
        background-color: var(--background-accent-main);
        border-radius: var(--border-radius-2);
        border: 0;
        color: var(--text-high-contrast);
    }
}

@layer utilities {

    [hidden] {
        display: none;
    }

    .visually-hidden {
        clip: rect(0 0 0 0);
        clip-path: inset(50%);
        height: 1px;
        overflow: hidden;
        position: absolute;
        white-space: nowrap;
        width: 1px;
    }

    .text-center {
        text-align: center;
    }

    .text-brand {
        color: var(--text-brand);
    }

    .text-high-contrast {
        color: var(--text-high-contrast);
    }

    .section-title {
        font-size: var(--font-size-heading-regular);
    }

    .background-base {
        background-color: var(--background-main);
    }

    .background-light {
        background-color: var(--background-light);
    }

    .background-extra-light {
        background-color: var(--background-extra-light);
    }

    .background-dark {
        background-color: var(--background-dark);
    }

    .background-extra-dark {
        background-color: var(--background-extra-dark);
    }

    .background-accent {
        background-color: var(--background-accent-dark);
    }

    .font-size-sm {
        font-size: var(--font-size-sm);
    }

    .font-size-regular {
        font-size: var(--font-size-regular);
    }

    .font-size-md {
        font-size: var(--font-size-md);
    }

    .font-size-lg {
        font-size: var(--font-size-lg);
    }

}